<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true">
        <div class="confirm-team-dialog">
            <p class="t1">确认接受主播昵称...的组队邀请吗? </p>
            <p class="t2">组队成功后将不可更改!</p>
            <div class="input-box">
                <textarea
                    type="text"
                    :value="confirmText"
                    placeholder="请手动输入“接受邀请”并点击确认完成组队操作"></textarea>
                <p @click="fastWrite">一键写入</p>
            </div>
            <div class="btn-box">
                <img
                    :src="requireImg('tab1/<EMAIL>')"
                    alt=""
                    @click="isShow = false">
                <img
                    :src="requireImg('tab1/<EMAIL>')"
                    alt=""
                    @click="toInvite">
            </div>
        </div>
    </modal-container>
</template>

<script setup>
const isShow = ref(false);
const confirmText = ref('');
const fastWrite = () => {
    confirmText.value = '';
    confirmText.value = '接受邀请';
};
const toInvite = () => {

};
useEventBus('confirm-team-dialog').on(({ show = true }) => {
    isShow.value = show;
});
</script>

<style lang="less" scoped>
.confirm-team-dialog {
    width: 375px;
    height: 220px;
    padding-top: 52px;
    .full-bg('@/assets/img/tab1/<EMAIL>');
    .t1 {
        font-size: 13px;
        font-weight: 500;
        text-align: center;
        color: #332e46;
        line-height: 17.5px;
    }
    .t2 {
        font-size: 13px;
        font-weight: 500;
        text-align: center;
        color: #e07134;
        line-height: 17.5px;
        margin-bottom: 24px;
    }
    .input-box {
        width: 200px;
        margin: 0 auto;
        textarea {
            border: none;
            color: #333;
            font-size: 13px;
            width: 200px;
            height: 30px;
            margin-bottom: 6px;
        }
        textarea::placeholder {
            opacity: 0.5;
            color: #606060;
            font-size: 12px;
        }
        p {
            font-size: 13px;
            font-weight: 500;
            text-align: right;
            color: #b83516;
        }
    }
    .btn-box {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-top: 70px;
        img {
            width: 128px;
            height: 38px;
            margin: 0 5px;
        }
    }
}
</style>
