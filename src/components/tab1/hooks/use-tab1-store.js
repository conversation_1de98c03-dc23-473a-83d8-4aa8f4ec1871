import { defineStore } from 'pinia';
import { getTeamStarlightRecord } from '@/api';

const useTab1Store = defineStore('tab1', () => {
    const recordTotalValue = ref(0);
    const recordList = ref([]);
    const getTeamStarlightRecordApi = async (payload) => {
        const loading = showLoading();
        const [{ code, data }] = await getTeamStarlightRecord(payload);
        loading.close();
        recordTotalValue.value = data.totalValue;
        recordList.value = data.list;
        // console.log('recordList', recordList.value);
        if (code === 0) {
            return data;
        }
        return {};
    };
    // const getMyStarlightApi = async (payload) => {
    //     const loading = showLoading();
    //     const [{ code, data }] = await getMyStarlight(payload);
    //     loading.close();
    //     console.log('getRankList', data);
    //     if (code === 0) {
    //         return data;
    //     }
    //     return {};
    // };

    return {
        getTeamStarlightRecordApi,
        // getMyStarlightApi,
        recordList,
        recordTotalValue,
    };
});

export default useTab1Store;
