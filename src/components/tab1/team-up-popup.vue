<!-- eslint-disable vue/no-parsing-error -->
<template>
    <popup-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div class="team-up-popup">
            <div class="top">
                <img
                    class="avatar"
                    :src="requireImg('default_avatar_no_compress.png')" />我的身份：导师<img
                        class="tips-icon"
                        :src="requireImg('tab1/<EMAIL>')"
                        @click="showTipsContent = !showTipsContent" />
                <div
                    v-show="showTipsContent"
                    ref="tipsContent"
                    class="tipsContent">
                    <p>
                        <span>导师定义：</span>满足【首次签约>6个月】&【近3个月均活跃天>15天】&【近3个月均总收礼>1w元】&【当前粉团>500】的达人
                    </p>
                    <p>
                        <span>学员人数：</span>满足【首次签约<3个月】&【精英及以下】&【考核B级及以上】的达人
                    </p>
                </div>
            </div>
            <div class="topbar">
                <div class="tab">
                    <p
                        :class="{ active: tabIndex === 0 }"
                        @click="changeTab(0)">
                        我发出邀请
                    </p>
                    <p
                        :class="{ active: tabIndex === 1 }"
                        @click="changeTab(1)">
                        我收到邀请
                    </p>
                </div>
                <div class="search">
                    <input
                        type="text"
                        placeholder="输入TTID搜索">
                </div>
            </div>
            <div class="user-box">
                <div
                    v-for="item in 20"
                    class="user">
                    <div class="user-pic">
                        <img
                            class="avatar"
                            :src="requireImg('default_avatar_no_compress.png')"
                            alt="">
                    </div>
                    <div class="user-name">放的开飞机上看见都是分开</div>
                    <img
                        class="btn"
                        :src="requireImg('tab1/<EMAIL>')"
                        @click="useEventBus('confirm-team-dialog').emit({ show: true })" />
                </div>
            </div>
        </div>
    </popup-container>
</template>

<script setup>
import { createExternalClickHandler } from '@/utils/index';

const isShow = ref(false);
const tabIndex = ref(0);
const showTipsContent = ref(false);
const tipsContent = ref();
const changeTab = (index) => {
    tabIndex.value = index;
};
let handler = null;
useEventBus('team-up-popup').on(async ({ show = true }) => {
    isShow.value = show;
    await nextTick();
    // 点击元素以外区域触发
    handler = createExternalClickHandler({
        target: tipsContent.value,
        excludeSelectors: ['.tipsContent', '.tips-icon'], // 支持多个排除元素
        callback: () => {
            // console.log('触发外部点击');
            if (showTipsContent.value) {
                showTipsContent.value = false;
            }
        // handler.stop(); // 可选：关闭后停止监听
        },
    });
    // 初始化显示并启动监听
    handler.start();
});
onMounted(() => {
    setTimeout(() => {
        // console.log('ss', tipsContent.value);
        // // 点击元素以外区域触发
        // const handler = createExternalClickHandler({
        //     target: document.querySelector('.tips-content'),
        //     excludeSelectors: [''], // 支持多个排除元素
        //     callback: () => {
        //         showTipsContent.value = false;
        //         // console.log('触发外部点击');
        //     // handler.stop(); // 可选：关闭后停止监听
        //     },
        // });

        // // 初始化显示并启动监听
        // // document.getElementById('target').style.display = 'block';
        // handler.start();
    }, 1000);
});
</script>

<style lang="less" scoped>
.team-up-popup {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 375px, 428px);
    padding-top: 55px;
    .top {
        width: 100%;
        display: flex;
        justify-content: center;
        font-size: 16px;
        font-weight: normal;
        text-align: left;
        color: #ffe29c;
        height: 28px;
        align-items: center;
        margin-bottom: 30px;
        position: relative;
        .avatar {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            margin-right: 6px;
        }
        .tips-icon {
            width: 13px;
            height: 13px;
            margin-left: 4px;
            margin-top: -10px;
        }
        .tipsContent {
            .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 291px, 148px);
            padding-top: 24px;
            padding-left: 22px;
            position: absolute;
            top: 20px;
            right: 20px;
            p {
                width: 250px;
                font-size: 12px;
                font-weight: normal;
                text-align: left;
                color: #5a5a5a;
                line-height: 18px;
                margin-bottom: 16px;
                span {
                    color: #df8c2a;
                }
            }
        }
    }
    .topbar {
        width: 100%;
        display: flex;
        justify-content: center;
        .tab {
            display: flex;
            justify-content: center;
            font-size: 13px;
            font-weight: normal;
            text-align: center;
            color: #bba6a1;
            height: 24px;
            line-height: 24px;
            margin-right: 20px;
            p {
                width: 80px;
            }
            .active {
                color: #fdff74;
                font-size: 14px;
            }
        }
        .search {
            .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 124px, 24px);
            padding-left: 4px;
            input {
                background: none;
                width: 90px;
                height: 24px;
                border: none;
                font-size: 12px;
                color: #fff;
            }
        }
    }
    .user-box {
        width: 100%;
        display: flex;
        justify-content: center;
        flex-flow: wrap;
        padding-top: 20px;
        .user {
            width: 70px;
            margin: 0 10px 14px;
            &-pic {
                width: 50px;
                height: 50px;
                // .full-bg('@/assets/img/tab1/<EMAIL>');
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 0 auto 3px;
                .avatar {
                    max-width: 100%;
                    max-height: 100%;
                    border-radius: 50%;
                }
            }
            &-name {
                font-size: 11px;
                font-weight: 400;
                text-align: center;
                color: #ffebc7;
                width: 100%;
                .one-line();
                margin-bottom: 8px;
            }
            .btn {
                width: 64px;
                height: 28px;
                margin: 0 auto;
            }
        }
    }
}
</style>
