<template>
    <popup-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div class="daily-challenge-popup">
            <div class="top">
                <div class="top-left">
                    <img
                        class="avatar"
                        :src="requireImg('default_avatar_no_compress.png')" />我的身份：导师
                </div>
                <div class="top-right">
                    今日星光值：9.99W<img
                        class="icon"
                        :src="requireImg('tab1/<EMAIL>')" />
                </div>
            </div>
            <div class="body">
                <p class="tips">每日自身星光值达到2000即可根据身份获得对应奖励</p>
                <div class="award">
                    <div class="award-left">
                        <Tab1-reward-item :item="rewardItem" />
                        <img
                            class="btn"
                            :src="requireImg('tab1/<EMAIL>')" />
                    </div>
                    <div class="award-right">
                        <Tab1-reward-item :item="rewardItem" />
                        <Tab1-reward-item :item="rewardItem" />
                        <img
                            class="btn"
                            :src="requireImg('tab1/<EMAIL>')" />
                    </div>
                </div>
                <p class="tips">每天24点,在完成每日挑战的达人中随机抽取20人额外发放星光幸运儿大v认证奖励!</p>
                <div class="lucky-ones">
                    <div class="user">
                        <div
                            v-for="item in 10"
                            class="user-item">
                            <div class="pic">
                                <img
                                    class="avatar"
                                    :src="requireImg('default_avatar_no_compress.png')" />
                            </div>
                            <div class="nickname">用户昵称昵称xxxx</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </popup-container>
</template>

<script setup>
const isShow = ref(false);
const rewardItem = { pic: 'default_avatar_no_compress.png', name: '奖励xxxxxx', desc: '奖励描述xxxxx' };
useEventBus('daily-challenge-popup').on(({ show = true }) => {
    isShow.value = show;
});
</script>

<style lang="less" scoped>
.daily-challenge-popup {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 375px, 456px);
    padding-top: 52px;
    .top {
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding-left: 25px;
        padding-right: 14px;
        &-left {
            display: flex;
            justify-content: flex-start;
            font-size: 16px;
            font-weight: normal;
            text-align: left;
            color: #ffe29c;
            height: 28px;
            align-items: center;
            .avatar {
                width: 28px;
                height: 28px;
                border-radius: 50%;
                margin-right: 6px;
            }
        }
        &-right {
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            color: #ffe29c;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: 28px;
            .icon {
                width: 13px;
                height: 13px;
                margin-left: 4px;
                margin-top: -10px;
            }
        }
    }
    .body {
        width: 100%;
        padding: 20px 30px 0;
        .tips {
            font-size: 13px;
            font-weight: normal;
            text-align: center;
            color: #d2cee5;
            margin-bottom: 14px;
        }
        .award {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            &-left {
                .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 104px, 154px);
                padding-top: 30px;
                position: relative;
                display: flex;
                justify-content: center;
            }
            &-right {
                .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 195px, 154px);
                display: flex;
                justify-content: center;
                padding-top: 30px;
                position: relative;
            }
            .btn {
                position: absolute;
                bottom: 8px;
                left: 50%;
                width: 74px;
                height: 24px;
                transform: translateX(-50%);
            }
        }
        .lucky-ones {
            padding-top: 33px;
            .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 317px, 101px);
            .user {
                width: 100%;
                overflow-x: scroll;
                overflow-y: hidden;
                height: 60px;
                display: flex;
                justify-content: flex-start;
                &-item {
                    width: 50px;
                    margin-right: 8px;
                    .pic {
                        width: 42px;
                        height: 42px;
                        margin: 0 auto;
                        img {
                            width: 100%;
                            height: 100%;
                            border-radius: 50%;
                        }
                    }
                    .nickname {
                        font-size: 10px;
                        font-weight: 400;
                        text-align: center;
                        .one-line();
                        color: #e5e5e5;
                        line-height: 18px;
                    }
                }
            }
        }
    }
}
</style>
