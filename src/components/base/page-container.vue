<template>
    <main
        ref="pageRef"
        class="absolute! left-0! top-0! h-full! w-full! overflow-x-hidden! overflow-y-auto!"
        @scroll="handleScroll">
        <slot />
    </main>
</template>

<script setup>
const pageRef = ref(null);
let scrollTop = 0;

onActivated(() => {
    pageRef.value.scrollTop = scrollTop;
});
onDeactivated(() => {
    scrollTop = pageRef.value.scrollTop;
});
const handleScroll = () => {
    const scrollWrap = pageRef.value;
    // console.log('scrollWrap.scrollTop', scrollWrap.scrollTop);
    // console.log('scrollWrap.clientHeight', scrollWrap.clientHeight);
    if (scrollWrap.scrollTop + scrollWrap.clientHeight >= scrollWrap.scrollHeight - 30) {
        console.log('scroll to bottom');
        useEventBus('scroll-to-bottom').emit();
    }
};
</script>
