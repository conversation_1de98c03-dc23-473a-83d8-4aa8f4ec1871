<template>
    <div
        class="tab4-rank-item"
        :class="{ 'is-self': isSelf }">
        <div class="rank-number">{{ rank }}</div>

        <div class="user-info">
            <div class="avatar">
                <img
                    :src="getAvatar(item.username)"
                    class="avatar"
                    @click="to<PERSON>erson(item.username)" />
                <room-status-anchor
                    v-if="item.channelInfo?.channelId && !isSelf"
                    :cid="item.channelInfo?.channelId"
                    :status="item.channelInfo?.status"
                    class="room-status-anchor" />
            </div>

            <div class="nickname">{{ safeOmitTxt(item.nickname) }}</div>
        </div>

        <div class="rank-value">
            <img
                class="mr-2 h-16 w-16"
                src="@/assets/img/tab3/<EMAIL>"
                alt="">{{ formatValue(item.value) }}
        </div>
    </div>
</template>

<script setup>
import { formatNumber } from '@/utils';

const props = defineProps({
    item: {
        type: Object,
        required: true,
    },
    rank: {
        type: Number,
        required: true,
    },
    type: {
        type: Number,
        required: true,
    },
    isSelf: {
        type: Boolean,
        default: false,
    },
});

function formatValue(value) {
    return formatNumber(value || 0);
}
</script>

<style lang="less" scoped>
.tab4-rank-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    width: 356px;
    height: 82px;
    background-image: url('@/assets/img/<EMAIL>');
    background-size: 100%;
    margin-bottom: 8px;
    margin-right: auto;
    margin-left: auto;

    &.is-self {
        background-image: url('@/assets/img/<EMAIL>');
        background-size: 100%;
        width: 375px;
        height: 87px;
        position: fixed;
        bottom: 0;
        right: 0;
        z-index: 100;
        margin-bottom: 0;
    }

    .rank-number {
        width: 30px;
        font-size: 16px;
        font-weight: bold;
        color: #fff;
        text-align: center;
        flex-shrink: 0;
    }

    .user-info {
        display: flex;
        align-items: center;
        flex: 1;
        margin-left: 15px;
        min-width: 0;

        .avatar {
            position: relative;
            width: 53px;
            height: 53px;
            border-radius: 50%;
            border: 1px solid rgba(255, 255, 255, 0.2);
            flex-shrink: 0;
        }

        .nickname {
            margin-left: 12px;
            font-size: 14px;
            color: #e5e5e5;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .rank-value {
        display: flex;
        font-size: 14px;
        color: #ffffff;
        font-weight: bold;
        flex-shrink: 0;
        margin-left: 15px;
    }
}
.room-status-anchor {
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
}
</style>
