
syntax = "proto3";

package activity;

service Activity {
  // 心动挑战页面数据
  rpc getHeartbeatChallengeData(InitReq) returns (GetHeartbeatChallengeDataResp) {};
  // 作品评分
  rpc setScore(SetScoreReq) returns (Empty) {};
  // 心动指数榜单
  rpc getRank(GetRankReq) returns (GetRankResp) {};
  // 初始化
  //rpc init(InitReq) returns (InitResp) {};
  // 用户榜单
  
  // 获取播报列表
  //rpc getBroadcastList(GetBroadcastListReq) returns (GetBroadcastListResp) {};
}

message Empty {}

message Success { 
  string data = 1 [ default = 'success' ];
}

message InitReq {
  uint32 uid = 1;
}

message InitResp {
  uint32 serverTime = 1; // 服务器时间
  uint32 startTime = 2; // 活动开始时间
  uint32 endTime = 3; // 活动结束时间
  UserInfo userInfo = 4;
}


/********** types **********/

message GuildInfo {
  string name = 1;
  uint32 guildId = 2; // 一般不外显
  uint32 displayId = 3;
}

message UserInfo {
  uint32 uid = 1;
  string username = 2;
  string alias = 3;
  string nickname = 4;
  uint32 sex = 5; // 非1为女
  optional GuildInfo guildInfo = 6; // 公会信息
  optional uint32 role = 7; // 用户角色，具体值由业务决定
}

enum ChannelStatus {
  LEAVE = 0; // 不在房
  STAY  = 1; // 在房
  WATCH = 2; // 看直播
  LIVE  = 3; // 直播中
  PK    = 4; // PK
}

message ChannelInfo {
  uint32 channelId = 1;
  ChannelStatus status = 2; // 在房状态
}

message MvpInfo {
  uint32 rank = 1;
  uint32 value = 2;
  string valueHuman = 3;
  UserInfo userInfo = 4;
}

message CommonRankItem {
  uint32 uid = 1;
  uint32 rank = 2; // 榜单排名
  string rankHuman = 3;
  uint32 value = 4; // 送礼/收礼值
  string valueHuman = 5;
  uint32 ltPrevValue = 6;
  string ltPrevValueHuman = 7;
  uint32 gtNextValue = 8;
  string gtNextValueHuman = 9;
  UserInfo userInfo = 10; // 用户信息
  optional ChannelInfo channelInfo = 11; // 可选 在房信息
  repeated MvpInfo mvpInfoList = 12; // 可选
}

message GetRankReq {
  uint32 page = 1 [ default = 1 ];
  uint32 size = 2 [ default = 10 ];
  uint32 uid = 3;           // 当前操作用户uid
  uint32 anchorUid = 4; // 当前开播房主id
}
message GetRankResp {
  uint32 total = 1;       // 总数
  repeated CommonRankItem list = 2; // 列表信息
  CommonRankItem self = 3;    // 底部我的排名信息
}

message GetBroadcastListReq {
  uint32 type = 1; // 抽奖类型: 
}
message GetBroadcastListResp {
  repeated Item list = 1;
  message Item {
    UserInfo user = 1;
    string str = 2;
    string rewardId = 3;
  }
}

message GetHeartbeatChallengeDataResp {
  UserInfo user = 1; // 当前用户信息
  uint32 value = 2; // 用户参与天数
  AnchorWork anchorWork = 3; // 作品
  int32 serverTime = 4; // 服务器时间
  uint32 startTime = 5; // 活动开始时间
  uint32 endTime = 6; // 活动结束时间
  message AnchorWork {
    UserInfo anchor = 1; // 主播用户信息
    uint32 workerUid = 2; // 主播uid
    string introduction = 3; // 主播简介
    string workLink = 4; // 作品链接
    optional string channelId = 5; // 开播房间id, 无开播时为空
  }
}

message SetScoreReq {
  uint32 uid = 1; // 打分者
  uint32 workerUid = 2; // 作品创作者
  uint32 score = 3; // 分数
}
