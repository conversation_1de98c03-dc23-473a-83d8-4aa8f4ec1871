const API = {
    init() {
        return {
            serverTime: new Date('2025/08/19 12:00:00').getTime() / 1000,
            startTime: new Date('2025/08/18 00:00:00').getTime() / 1000,
            endTime: new Date('2025/08/24 23:59:59').getTime() / 1000,
            userInfo: {
                uid: 2416206,
                username: 'tt110200509',
                alias: 'Mock-alias',
                nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                sex: 1,
            },
            identity: 1,
            level: 2,
            bindInfo: {},
            teamBuff: 2,
            dailyTeamRaceId: 'B',
        };
    },
    invite() {
        return {};
    },
    acceptInvite() {
        return {};
    },
    getInviteList() {
        return {
            list: [
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 1,
                    acceptStatus: 1,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 1,
                    acceptStatus: 1,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 1,
                    acceptStatus: 1,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 1,
                    acceptStatus: 1,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 1,
                    acceptStatus: 1,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 1,
                    acceptStatus: 1,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 1,
                    acceptStatus: 1,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 1,
                    acceptStatus: 1,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 1,
                    acceptStatus: 1,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 1,
                    acceptStatus: 1,
                },
            ],
            total: 2,
        };
    },
    getAcceptList() {
        return {
            list: [
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 2,
                    acceptStatus: 2,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 2,
                    acceptStatus: 2,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 2,
                    acceptStatus: 2,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 2,
                    acceptStatus: 2,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 2,
                    acceptStatus: 2,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 2,
                    acceptStatus: 2,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 2,
                    acceptStatus: 2,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 2,
                    acceptStatus: 2,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 2,
                    acceptStatus: 2,
                },
                {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    level: 2,
                    identity: 2,
                    acceptStatus: 2,
                },
            ],
            total: 2,
        };
    },
    getTeamStarlightRecord() {
        return {
            list: [
                { id: 1, value: 2, limit: 1, completedCount: 100, curValue: 2 },
                { id: 1, value: 2, limit: 1, completedCount: 5, curValue: 2 },
                { id: 1, value: 2, limit: 1, completedCount: 1, curValue: 2 },
                { id: 1, value: 2, limit: 1, completedCount: 1, curValue: 2 },
                { id: 1, value: 2, limit: 1, completedCount: 1, curValue: 2 },
                { id: 1, value: 2, limit: 1, completedCount: 1, curValue: 2 },
                { id: 1, value: 2, limit: 1, completedCount: 1, curValue: 2 },
                { id: 1, value: 2, limit: 1, completedCount: 1, curValue: 2 },
                { id: 1, value: 2, limit: 1, completedCount: 1, curValue: 2 },
                { id: 1, value: 2, limit: 1, completedCount: 1, curValue: 2 },
            ],
            totalValue: 1,
        };
    },
    getMyStarlight() {
        return { myStarlight: 2, rewardStatus: 1 };
    },
    receiveDailyReward() {
        return {};
    },
    getDailyLuckyList() {
        return { list: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}] };
    },
    getStarlightRank() {
        return {
            total: 50,
            list: [
                {
                    rank: '1',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '2',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '3',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '4',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '5',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '6',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '7',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '8',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '9',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
                {
                    rank: '10',
                    value: 1,
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    discipleInfo: {
                        userInfo: {
                            uid: 2416206,
                            username: 'tt110200509',
                            alias: 'Mock-alias',
                            nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                            sex: 1,
                        },
                        channelInfo: {},
                        level: 2,
                    },
                },
            ],
            self: {
                userInfo: {
                    uid: 2416206,
                    username: 'tt110200509',
                    alias: 'Mock-alias',
                    nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                    sex: 1,
                },
                rank: '2',
                value: 2,
                prevDescribe: 'Mock-prevDescribe',
                nextDescribe: 'Mock-nextDescribe',
                discipleInfo: {
                    userInfo: {
                        uid: 2416206,
                        username: 'tt110200509',
                        alias: 'Mock-alias',
                        nickname: '哈喽喽喽喽喽喽喽喽喽喽喽喽',
                        sex: 1,
                    },
                    channelInfo: {},
                    level: 2,
                },
            },
        };
    },
};
const getMockData = (type, payload) =>
    new Promise((resolve) => {
        const delay = Math.round(Math.random() * 10) * 50; // 模拟访问延迟
        let data;
        if (typeof API[type] === 'function')
            data = API[type](payload);
        else data = API[type];

        // eslint-disable-next-line no-console
        console.log(
            `模拟接口请求名称<=== ${type} delay: ${delay} ms; payload: ${JSON.stringify(payload)}`,
        );
        window.setTimeout(() => {
            // eslint-disable-next-line no-console
            console.log('模拟接口请求返回===>', data);
            resolve({
                code: 0,
                data,
                msg: '',
            });
        }, delay);
    });
export default getMockData;
